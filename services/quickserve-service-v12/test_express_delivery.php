<?php

/**
 * Test script to verify express delivery charge functionality
 * Tests that express charges are applied when ordering within express window
 */

require_once __DIR__ . '/bootstrap/app.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

echo "=== Express Delivery Charge Test ===\n\n";

// Test configuration
$baseUrl = 'http://127.0.0.1:8000/api/v2/order-management/create';
$testCustomerId = 1;
$testCompanyId = 8163;
$testUnitId = 8163;
$testKitchenCode = 1;

// First, let's check the current express settings
echo "Checking express delivery settings...\n";

$expressSettings = [
    'K1_LUNCH_EXPRESS_EXTRA_DELIVERY_CHARGE',
    'EXPRESS_EXTENDED_ENABLED',
    'EXPRESS_EXTENDED_END_TIME'
];

foreach ($expressSettings as $setting) {
    $value = DB::table('settings')
        ->where('setting_key', $setting)
        ->where('company_id', $testCompanyId)
        ->value('setting_value');
    
    if ($value === null) {
        $value = DB::table('settings')
            ->where('setting_key', $setting)
            ->whereNull('company_id')
            ->value('setting_value');
    }
    
    echo "  $setting: " . ($value ?: 'Not set') . "\n";
}

// Check cutoff settings
$cutoffSettings = DB::table('cutoff_settings')
    ->where('company_id', $testCompanyId)
    ->where('kitchen_id', $testKitchenCode)
    ->where('meal_type', 'lunch')
    ->first(['cut_off_time', 'cut_off_day']);

if ($cutoffSettings) {
    echo "  Lunch Cutoff Time: {$cutoffSettings->cut_off_time}\n";
    echo "  Lunch Cutoff Day: {$cutoffSettings->cut_off_day}\n";
} else {
    echo "  No cutoff settings found for lunch\n";
}

echo "\nCurrent time: " . now()->format('Y-m-d H:i:s') . "\n";
echo str_repeat("-", 50) . "\n\n";

// Test 1: Create express order with future delivery dates
echo "Test 1: Creating express order for future delivery dates...\n";

$orderPayload = [
    'is_express' => true,
    'customer_id' => $testCustomerId,
    'user_id' => 1,
    'company_id' => $testCompanyId,
    'unit_id' => $testUnitId,
    'fk_kitchen_code' => $testKitchenCode,
    'customer_name' => 'Test Customer',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '9876543210',
    'customer_address' => 'Test Address',
    'location_code' => 17,
    'location_name' => 'Default Location',
    'city' => 9,
    'city_name' => 'Mumbai',
    'start_date' => '2025-09-08',
    'selected_days' => [1, 2], // Monday, Tuesday
    'subscription_days' => 2,
    'meals_by_date' => [
        [
            'date' => '2025-09-08',
            'meals' => [
                [
                    'product_code' => 336,
                    'quantity' => 1,
                    'charge' => 50 // Express charge provided in request
                ]
            ]
        ],
        [
            'date' => '2025-09-09',
            'meals' => [
                [
                    'product_code' => 336,
                    'quantity' => 1,
                    'charge' => 0 // No express charge for second day
                ]
            ]
        ]
    ],
    'promo_code' => [
        'applied' => false,
        'code' => null
    ]
];

// Make API call
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($orderPayload));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json',
    'Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJVa29NTHlmWkRXQ1k2TkdQQy1yMEtQb09kdURDd21KQkdjaVdwenFzMC04In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AgQpNWceW0lM78lmkZvWhz-X4veI-dQwUb3_Vbg87jaDn7F8E6uqU3Gm3L4DokGhjpyBsWvc4uBSpYlw2xJHxmI33RE_AxYaEMzQVCaTDX95gWyoAtfRzsd20zuEuhUbyFA5a3XZ4H7EsJYV43jlMFDvGwlb0v3C_b-h0hhAYudwS92hS8GIn4f2EFewys5ZsVyN1IciiEgG1YVwvkUQQP52Hf4FjE2nDlv7twEAT4LZM0ZbyY88ZqXmTbUp-K-4L4sEzIwtnCtbJ7iCqvsAL4ZyXkrTpDE1GnzMlSvBsFc1-eQj_-rOwLYEgb24PIez-kcPcf6rQE2k2TiZctkbSQ'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Status: $httpCode\n";

if ($response === false) {
    echo "❌ CURL Error occurred\n";
    exit(1);
}

$responseData = json_decode($response, true);

if ($httpCode === 201 && isset($responseData['success']) && $responseData['success']) {
    echo "✅ Express order created successfully!\n";
    
    $totalAmount = $responseData['data']['total_amount'] ?? 0;
    $orderNo = $responseData['data']['order_no'] ?? null;
    
    echo "\nOrder Analysis:\n";
    echo "  Order No: $orderNo\n";
    echo "  Total Amount: ₹$totalAmount\n";
    
    // Check if express charges were applied
    $tempPreOrderIds = $responseData['data']['temp_pre_order_ids'] ?? [];
    
    if (!empty($tempPreOrderIds)) {
        echo "\nTemp Pre-Order Analysis:\n";
        
        $totalExpressCharges = 0;
        $totalMealAmount = 0;
        
        foreach ($tempPreOrderIds as $tempPreOrderData) {
            $tempPreOrderId = $tempPreOrderData['id'];
            
            // Check temp_pre_orders record
            $tempPreOrder = DB::table('temp_pre_orders')
                ->where('pk_order_no', $tempPreOrderId)
                ->first([
                    'delivery_date', 
                    'amount', 
                    'express_delivery_charge',
                    'tax_amount',
                    'total_amount'
                ]);
            
            if ($tempPreOrder) {
                echo "  Date {$tempPreOrder->delivery_date}:\n";
                echo "    Meal Amount: ₹{$tempPreOrder->amount}\n";
                echo "    Express Charge: ₹{$tempPreOrder->express_delivery_charge}\n";
                echo "    Tax Amount: ₹{$tempPreOrder->tax_amount}\n";
                echo "    Total Amount: ₹{$tempPreOrder->total_amount}\n";
                
                $totalExpressCharges += $tempPreOrder->express_delivery_charge;
                $totalMealAmount += $tempPreOrder->amount;
            }
        }
        
        echo "\nSummary:\n";
        echo "  Total Meal Amount: ₹$totalMealAmount\n";
        echo "  Total Express Charges: ₹$totalExpressCharges\n";
        echo "  Final Total: ₹$totalAmount\n";
        
        if ($totalExpressCharges > 0) {
            echo "\n🎉 SUCCESS: Express delivery charges applied correctly!\n";
            echo "   Express charge of ₹$totalExpressCharges was added to the order.\n";
        } else {
            echo "\n❌ ISSUE: No express delivery charges were applied.\n";
            echo "   This might indicate the express window logic is not working.\n";
        }
    }
    
} else {
    echo "❌ Express order creation failed!\n";
    echo "Response: $response\n";
    
    if (isset($responseData['message'])) {
        echo "Error: {$responseData['message']}\n";
    }
}

echo "\n" . str_repeat("=", 60) . "\n";

// Test 2: Check application logs for express evaluation
echo "\nChecking application logs for express evaluation...\n";
echo "(Look for 'Express evaluation for meal type' entries in your Laravel logs)\n";

echo "\n=== Test Complete ===\n";
