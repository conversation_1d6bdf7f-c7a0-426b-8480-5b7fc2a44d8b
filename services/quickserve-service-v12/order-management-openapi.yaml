openapi: 3.0.3
info:
  title: Order Management API
  description: |
    Comprehensive order management system for QuickServe food delivery platform with advanced cancellation policies.

    ## Complete Order Journey (Updated with Temporary Tables & Wallet Integration)
    1. **Create Pre-Order** - Creates temp_pre_orders, temp_order_payment, payment_transaction (initiated)
    2. **Payment Processing** - Mobile app uses Payment Service v12 APIs
    3. **Payment Success** - Updates payment_transaction, creates payment_transfered (Razorpay), updates temp_order_payment
    4. **Order Creation** - Creates multiple orders (15 days = 12 weekday orders) and order_details (3 items × 12 orders = 36 records)
    5. **Wallet Locking** - Locks wallet amount for each order for cancellation tracking
    6. **Order Fulfillment** - All orders ready for delivery with status "New" (not "Confirmed")

    ## Mobile App Integration
    **Step 1:** POST /order-management/create → Get payment_service_transaction_id
    **Step 2:** Use Payment Service v12 APIs with transaction ID
    **Step 3:** Orders automatically created when payment succeeds with wallet locking

    ## Advanced Cancellation System
    ### Time-Based Refund Policies:
    1. **Before Cutoff Time** → 100% refund + unlock wallet amount
    2. **Partial Refund Window (00:01:00-08:00:00)** → Breakfast: 0%, Lunch: 50% + unlock wallet
    3. **After 08:00:00** → No cancellation allowed

    ### Settings-Based Configuration:
    - All cutoff times and refund percentages configurable via database settings
    - Kitchen-specific and meal-specific policies
    - Cutoff day calculation (0=same day, 1=day before, etc.)

    ## Double Promo Support
    ### Simultaneous Discount Application:
    1. **Manual Promo Codes** → Customer-entered codes like "SPECIAL", "WELCOME10"
    2. **Plan-Based Discounts** → Automatic discounts based on subscription duration (e.g., 5-day plan gets 15% off)
    3. **Combined Application** → Both discounts can be applied together for maximum savings

    ### Discount Calculation Order:
    1. Manual promo code discount applied first on original amount
    2. Plan-based discount applied on remaining amount after manual discount
    3. Total discount = Manual discount + Plan discount
    4. Final amount = Original amount - Total discount

    ### Response Structure:
    - `discount_details`: Complete breakdown of both discount types
    - `manual_promo_code`: Applied manual promo code (if any)
    - `system_promo_code`: Applied plan-based promo code (if any)
    - `total_discount_amount`: Sum of both discounts

    ## Service Separation
    - **Order Management**: QuickServe Service (temp tables → actual orders)
    - **Payment Processing**: Payment Service v12 (unchanged)
    - **Wallet Management**: Integrated locking/unlocking system
    - **Database**: Proper transaction handling with temp tables and wallet tracking

  version: 2.0.0
  contact:
    name: QuickServe API Support
    email: <EMAIL>
servers:
  - url: http://*************:8003/api/v2
    description: Development server
  - url: https://api.quickserve.com/v2
    description: Production server

# Configuration Settings for Cancellation Policies
x-cancellation-settings:
  description: |
    Database settings that control the time-based cancellation policies.
    All settings are stored in the `settings` table and can be modified without code changes.

  cutoff-times:
    K1_BREAKFAST_ORDER_CUT_OFF_TIME: "00:01:00"
    K1_BREAKFAST_ORDER_CUT_OFF_DAY: "0"
    K1_LUNCH_ORDER_CUT_OFF_TIME: "00:01:00"
    K1_LUNCH_ORDER_CUT_OFF_DAY: "0"

  cancellation-cutoffs:
    K1_BREAKFAST_ORDER_CANCEL_CUT_OFF_TIME: "00:01:00"
    K1_BREAKFAST_ORDER_CANCEL_CUT_OFF_DAY: "0"
    K1_LUNCH_ORDER_CANCEL_CUT_OFF_TIME: "00:01:00"
    K1_LUNCH_ORDER_CANCEL_CUT_OFF_DAY: "0"

  refund-percentages:
    BREAKFAST_PARTIAL_REFUND_PERCENTAGE: "0"    # 0% refund for breakfast in partial window
    LUNCH_PARTIAL_REFUND_PERCENTAGE: "50"       # 50% refund for lunch in partial window

  time-windows:
    CANCELLATION_PARTIAL_REFUND_START_TIME: "00:01:00"
    CANCELLATION_PARTIAL_REFUND_END_TIME: "08:00:00"
    CANCELLATION_NO_REFUND_START_TIME: "08:00:01"

  feature-flags:
    ENABLE_TIME_BASED_CANCELLATION: "yes"
    ENABLE_WALLET_LOCKING: "yes"
    ENABLE_AUTOMATIC_REFUND: "yes"

paths:
  /order-management/create:
    post:
      tags:
        - Order Management
      summary: Create a new pre-order with subscription
      description: |
        Creates a pre-order in temporary tables and initiates payment with Payment Service v12.

        **What happens:**
        1. Creates record in `temp_pre_orders`
        2. Creates record in `temp_order_payment` (status: pending)
        3. Creates record in `payment_transaction` (status: initiated)
        4. Calls Payment Service v12 to initiate payment
        5. Returns payment URLs for mobile app

        **After payment success:**
        - Creates multiple orders (one per delivery day based on start_date and selected_days)
        - Creates order_details (fetches meal items from product_planner table)
        - Updates all payment tables
        - Locks wallet amounts for cancellation tracking

        **Order Placement Fixes (v2.0):**
        - ✅ order_status = "New" (not "Confirmed")
        - ✅ payment_mode = "online" (not gateway name like "razorpay")
        - ✅ temp_pre_order.due_date = NULL (not current date)
        - ✅ temp_pre_order.city_name = actual city name from database (not city ID)
        - ✅ temp_pre_order.food_preference = "[]" (not "veg")
        - ✅ temp_pre_order.delivery_time = NULL, delivery_end_time = NULL
        - ✅ temp_pre_order.order_days = CSV format (not JSON array)
        - ✅ temp_order_payment = separate records for each meal (not clubbed)
        - ✅ Wallet locking during order creation for cancellation tracking

        **New Features:**
        - Uses start_date (YYYY-MM-DD) instead of current date
        - Selected_days array (0=Sunday, 6=Saturday) for specific day selection
        - Fetches meal items from product_planner table automatically
        - Prevents duplicate orders for same day/meal
        - Implements cut-off restrictions based on K1_BREAKFAST_ORDER_CUT_OFF_DAY/TIME settings
        - Supports multiple meals in cart via meals array
        - Fetches customer details (name, email, phone) from customer table
        - Fetches product details (name, description) from products table
        - **Database Validation:** city and location_code are validated against database tables
        - **Automatic Name Resolution:** city_name and location_name are fetched from database (not from request)
        - **Error Handling:** Returns 422 error for invalid city or location codes
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrderRequest'
            example:
              customer_id: 3800
              customer_address: "123 Test Street, Test Area, Test City - 400001"
              location_code: 1001
              city: 1
              meals:
                - product_code: 342
                  quantity: 1
                - product_code: 343
                  quantity: 1
              start_date: "2025-01-27"
              selected_days: [1, 2, 3, 4, 5]
              delivery_time: "08:00:00"
              delivery_end_time: "09:00:00"
              food_preference: "veg"
              promo_code:
                applied: true
                code: "SPECIAL"
              subscription_days: 15
            examples:
              with_promo_code:
                summary: Order with promo code
                value:
                  customer_id: 3800
                  customer_address: "123 Test Street, Test Area, Test City - 400001"
                  location_code: 1001
                  city: 1
                  meals:
                    - product_code: 342
                      quantity: 1
                  start_date: "2025-01-27"
                  selected_days: [1, 2, 3, 4, 5]
                  delivery_time: "08:00:00"
                  delivery_end_time: "09:00:00"
                  food_preference: "veg"
                  promo_code:
                    applied: true
                    code: "SPECIAL"
                  subscription_days: 15
              without_promo_code:
                summary: Order without promo code
                value:
                  customer_id: 3800
                  customer_address: "123 Test Street, Test Area, Test City - 400001"
                  location_code: 1001
                  city: 1
                  meals:
                    - product_code: 342
                      quantity: 1
                  start_date: "2025-01-27"
                  selected_days: [1, 2, 3, 4, 5]
                  delivery_time: "08:00:00"
                  delivery_end_time: "09:00:00"
                  food_preference: "veg"
                  subscription_days: 15
              promo_code_not_applied:
                summary: Order with promo code not applied
                value:
                  customer_id: 3800
                  customer_address: "123 Test Street, Test Area, Test City - 400001"
                  location_code: 1001
                  city: 1
                  meals:
                    - product_code: 342
                      quantity: 1
                  start_date: "2025-01-27"
                  selected_days: [1, 2, 3, 4, 5]
                  delivery_time: "08:00:00"
                  delivery_end_time: "09:00:00"
                  food_preference: "veg"
                  promo_code:
                    applied: false
                    code: null
                  subscription_days: 15
      responses:
        '201':
          description: Pre-order created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatePreOrderResponse'
              example:
                success: true
                message: "Pre-order created successfully"
                data:
                  temp_pre_order_id: 46154
                  order_no: "ORD202507221240574531"
                  customer_id: 3800
                  amount: 160
                  status: "pending"
                  start_date: "2025-01-27"
                  selected_days: [1, 2, 3, 4, 5]
                  subscription_days: 5
                  calculated_delivery_dates: ["2025-01-27", "2025-01-28", "2025-01-29", "2025-01-30", "2025-01-31"]
                  discount_details:
                    manual_discount:
                      applied: true
                      amount: 25.00
                      code: "SPECIAL"
                      details:
                        type: "percentage"
                        value: 10
                        applied_discount: 25.00
                    plan_discount:
                      applied: true
                      amount: 15.00
                      code: "PLAN5DAY"
                      details:
                        type: "fixed"
                        value: 15
                        applied_discount: 15.00
                    total_discount: 40.00
                  manual_promo_code: "SPECIAL"
                  system_promo_code: "PLAN5DAY"
                  total_discount_amount: 40.00
                  payment_transaction_id: 30609
                  payment_service_transaction_id: "TXN30610"
                  payment_urls:
                    process_payment: "http://*************:8002/api/v2/payments/TXN30610/process"
                    payment_status: "http://*************:8002/api/v2/payments/TXN30610"
                    order_status: "http://*************:8003/api/v2/order-management/pre-order-status/ORD202507221240574531"
        '400':
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation failed - Invalid city or location
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                invalid_city:
                  summary: Invalid City ID
                  value:
                    success: false
                    message: "Invalid city ID: 999. City not found or inactive for the specified company and unit."
                    error_code: "INVALID_CITY"
                invalid_location:
                  summary: Invalid Location Code
                  value:
                    success: false
                    message: "Invalid location code: 9999. Location not found or inactive for the specified company and unit."
                    error_code: "INVALID_LOCATION"
                invalid_promo_code:
                  summary: Invalid Promo Code
                  value:
                    success: false
                    message: "Invalid or expired promo code: INVALID123"
                    error_code: "INVALID_PROMO_CODE"
                promo_discount_error:
                  summary: Promo Code Discount Error
                  value:
                    success: false
                    message: "Minimum order value of ₹100 required for this promo code"
                    error_code: "PROMO_DISCOUNT_ERROR"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /order-management/details/{orderNo}:
    get:
      tags:
        - Order Management
      summary: Get order details
      description: Retrieves complete order information including meal items and payment status
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          example: "ORD202507220800028674"
      responses:
        '200':
          description: Order details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderDetailsResponse'
        '404':
          description: Order not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /order-management/customer/{customerId}:
    get:
      tags:
        - Order Management
      summary: Get customer orders with cancellation status
      description: |
        Retrieves paginated list of orders for a customer with real-time cancellation eligibility.

        **New Features:**
        - ✅ `is_cancellable` flag for each order based on time-based policies
        - ✅ `cancellation_policy` object with refund percentage and policy details
        - ✅ Real-time calculation based on current time and settings
        - ✅ Meal-specific cancellation policies (breakfast vs lunch)

        **Cancellation Policies:**
        - **Before Cutoff**: 100% refund allowed
        - **Partial Window (00:01-08:00)**: Breakfast 0%, Lunch 50%
        - **After 08:00**: No cancellation allowed
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: integer
          example: 3800
        - name: per_page
          in: query
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 100
        - name: status
          in: query
          schema:
            type: string
            enum: [New, Confirmed, Scheduled, Payment Failed, Cancelled, Delivered]
      responses:
        '200':
          description: Customer orders retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerOrdersResponse'

  /order-management/cancellation-settings:
    get:
      tags:
        - Order Management
      summary: Get cancellation policy settings
      description: |
        Retrieves current cancellation policy settings from database.

        **Returns:**
        - Cutoff times for each meal type
        - Refund percentages for partial refund window
        - Time windows for different policies
        - Feature flags for cancellation system
      responses:
        '200':
          description: Cancellation settings retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CancellationSettingsResponse'

  /order-management/pre-order-status/{orderNo}:
    get:
      tags:
        - Order Management
      summary: Get pre-order status and progress
      description: |
        Retrieves complete pre-order status including:
        - Temp pre-order details
        - Payment status (temp_order_payment + payment_transaction)
        - Created orders count (after payment success)
        - Order details count (meal items created)
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          example: "ORD202507221240574531"
      responses:
        '200':
          description: Pre-order status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreOrderStatusResponse'
              example:
                success: true
                data:
                  pre_order:
                    temp_pre_order_id: 46154
                    order_no: "ORD202507221240574531"
                    customer_id: 3800
                    customer_name: "Customer User"
                    product_name: "International Breakfast Subscription"
                    amount: "200.00"
                    order_status: "New"
                    days_preference: "1,2,3,4,5"
                    order_days: ["2025-07-22", "2025-07-23", "2025-07-24", "2025-07-25", "2025-07-28"]
                    created_date: "2025-07-22 18:10:57"
                  payment_status:
                    temp_payment_status: "success"
                    transaction_status: "completed"
                    gateway: "razorpay"
                    gateway_transaction_id: "TXN30610"
                  orders_created:
                    count: 12
                    order_details_count: 36
                    status: "completed"
        '404':
          description: Pre-order not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /payment/status/{orderNo}:
    get:
      tags:
        - Payment Management
      summary: Get payment status
      description: Retrieves current payment status for an order
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          example: "ORD202507220800028674"
      responses:
        '200':
          description: Payment status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentStatusResponse'
        '404':
          description: Order not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /payment/webhook:
    post:
      tags:
        - Payment Processing
      summary: Payment gateway webhook
      description: |
        Handles payment confirmation from payment gateways.
        This endpoint automatically updates order status when payment is confirmed.
        **No additional API calls needed after payment confirmation.**
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentWebhookRequest'
      responses:
        '200':
          description: Webhook processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebhookResponse'
        '401':
          description: Invalid signature
        '500':
          description: Webhook processing failed

  /payment/callback:
    post:
      tags:
        - Payment Processing
      summary: Payment callback handler
      description: Handles payment gateway callbacks (redirects)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentCallbackRequest'
      responses:
        '200':
          description: Callback processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebhookResponse'

  /payment/simulate/success:
    post:
      tags:
        - Testing
      summary: Simulate payment success
      description: Testing endpoint to simulate successful payment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                order_no:
                  type: string
                  example: "ORD202507220800028674"
              required:
                - order_no
      responses:
        '200':
          description: Payment simulation completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentSimulationResponse'

  /payment/simulate/failure:
    post:
      tags:
        - Testing
      summary: Simulate payment failure
      description: Testing endpoint to simulate payment failure
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                order_no:
                  type: string
                  example: "ORD202507220800028674"
              required:
                - order_no
      responses:
        '200':
          description: Payment failure simulation completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentSimulationResponse'

  /order-management/payment-success/{orderNo}:
    post:
      tags:
        - Payment Callbacks
      summary: Handle payment success callback
      description: |
        Called by Payment Service v12 when payment is successful.
        Automatically updates order status to "Confirmed" and creates recurring orders.
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          example: "ORD202507220800028674"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                payment_service_transaction_id:
                  type: string
                  example: "15234"
                gateway:
                  type: string
                  example: "razorpay"
                amount:
                  type: number
                  example: 125.00
      responses:
        '200':
          description: Payment success processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentCallbackResponse'
        '404':
          description: Order not found
        '500':
          description: Processing failed

  /order-management/payment-failure/{orderNo}:
    post:
      tags:
        - Payment Callbacks
      summary: Handle payment failure callback
      description: |
        Called by Payment Service v12 when payment fails.
        Updates order status to "Payment Failed".
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          example: "ORD202507220800028674"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                payment_service_transaction_id:
                  type: string
                  example: "15234"
                gateway:
                  type: string
                  example: "razorpay"
                failure_reason:
                  type: string
                  example: "Insufficient funds"
      responses:
        '200':
          description: Payment failure processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentCallbackResponse'
        '404':
          description: Order not found
        '500':
          description: Processing failed

  /order-management/cancel/{orderNo}:
    post:
      tags:
        - Order Management
      summary: Cancel order with time-based refund processing
      description: |
        Advanced order cancellation with time-based refund policies and wallet management.

        **Time-Based Refund Policies:**
        1. **Before Cutoff Time** → 100% refund + unlock wallet amount
        2. **Partial Refund Window (00:01:00-08:00:00)** → Breakfast: 0%, Lunch: 50% + unlock wallet
        3. **After 08:00:00** → No cancellation allowed

        **Enhanced Features:**
        - ✅ Time-based refund calculation based on meal type
        - ✅ Automatic wallet unlocking for locked amounts
        - ✅ Settings-driven cancellation policies
        - ✅ Meal-specific filtering (breakfast, lunch, dinner)
        - ✅ Detailed refund breakdown in response
        - ✅ Only cancels current or future date orders
        - ✅ Prevents cancellation of prepared/dispatched orders

        **Wallet Integration:**
        - Unlocks previously locked wallet amounts
        - Credits refund amount to customer wallet
        - Maintains detailed transaction history
        - ✅ Credits refund to customer wallet automatically
        - ✅ Updates order status to 'Cancelled'
        - ✅ Updates order_details and kitchen_data
        - ✅ Supports partial cancellation (specific dates)

        **Validation Rules:**
        - Orders must be in 'New', 'Confirmed', or 'Processing' status
        - Order date must be today or future date
        - Cannot cancel 'Prepared', 'Dispatched', or 'Delivered' orders

        **Refund Process:**
        1. Calculates total refund: `amount + tax + delivery_charges + service_charges - applied_discount`
        2. Credits amount to customer_wallet with `amount_type = 'cr'`
        3. Creates wallet transaction record with refund details
        4. Updates all related tables (orders, order_details, kitchen_data)
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          description: Order number to cancel
          example: "XAJE250724"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CancelOrderRequest'
      responses:
        '200':
          description: Order cancelled successfully with refund processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CancelOrderResponse'
        '400':
          description: Invalid request - orders cannot be cancelled (already prepared/delivered)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                message: "Cannot cancel orders that are already prepared, dispatched, or delivered."
        '404':
          description: No eligible orders found for cancellation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                message: "No eligible orders found for cancellation. Orders can only be cancelled for current or future dates."
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
        '500':
          description: Cancellation processing failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /order-management/swap/{orderNo}:
    post:
      tags:
        - Order Management
      summary: Swap order product with another product from the same category
      description: |
        Allows customers to swap their meal with another product from the same category.
        Updates both orders and order_details tables while maintaining data integrity.

        **Key Features:**
        - ✅ Product category validation - Only same category swaps allowed
        - ✅ Order status validation - Only swappable orders (not delivered/cancelled)
        - ✅ Price difference calculation - Automatic price adjustment
        - ✅ Swap charges support - Additional charges for premium swaps
        - ✅ Tax recalculation - Updated tax based on new amount
        - ✅ Order details update - All related records updated
        - ✅ Audit logging - Complete swap history tracking
        - ✅ Transaction safety - Database rollback on errors

        **Validation Rules:**
        - Products must belong to the same category/type
        - Order must not be Cancelled, Delivered, or Complete
        - Order must not be Delivered, Failed, or Dispatched
        - Cannot swap orders for past dates
        - Both current and new products must be active
        - New product must have is_swappable = true
        - Veg/Non-veg classification must match

        **Price Calculation:**
        ```
        New Amount = Old Amount + Price Difference + Swap Charges
        ```

        **Database Updates:**
        - **Orders table:** product_code, product_name, amount, tax, remark
        - **Order_details table:** All product fields updated proportionally

        **Example:**
        - Old Product: Poha (₹150) → New Product: Upma (₹200)
        - Swap Charges: ₹25
        - Total Change: ₹75 (₹50 price diff + ₹25 swap charges)
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          description: Order number to swap
          example: "QA93250725"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SwapOrderRequest'
      responses:
        '200':
          description: Order swapped successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SwapOrderResponse'
        '400':
          description: Invalid request - order not swappable or products not compatible
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                order_not_swappable:
                  summary: Order cannot be swapped
                  value:
                    success: false
                    message: "Order cannot be swapped due to its current status"
                    data:
                      order_id: 127810
                      order_status: "Delivered"
                      delivery_status: "Delivered"
                products_not_compatible:
                  summary: Products not in same category
                  value:
                    success: false
                    message: "Products are not in the same category or not swappable"
                    data:
                      current_product:
                        code: 341
                        name: "Poha"
                        category: "Breakfast"
                        type: "Meal"
                      new_product:
                        code: 355
                        name: "Biryani"
                        category: "Lunch"
                        type: "Meal"
        '404':
          description: Order or product not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                order_not_found:
                  summary: Order not found
                  value:
                    success: false
                    message: "Order not found for the specified date"
                    data:
                      order_no: "QA93250725"
                      order_date: "2025-09-03"
                      meal_type: null
                product_not_found:
                  summary: Product not found
                  value:
                    success: false
                    message: "New product not found"
                    data:
                      new_product_code: 342
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
        '500':
          description: Swap processing failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /order-management/deliver/{orderId}:
    post:
      summary: Mark order as delivered
      description: |
        Mark an order as delivered and trigger wallet processing.

        **Key Features:**
        - ✅ Updates order status to 'Complete' and delivery status to 'Delivered'
        - ✅ Triggers OrderDeliveredEvent for wallet deduction processing
        - ✅ Automatic wallet deduction for meal amount (excluding delivery charges)
        - ✅ Prevents duplicate wallet deductions for same order
        - ✅ Validates order status before marking as delivered

        **Wallet Processing:**
        - Creates debit entry in customer_wallet table
        - Deduction amount = net_amount - delivery_charges
        - Only processes if order was paid via wallet (amount_paid = 1)
        - Prevents duplicate deductions with reference_no validation

        **Order Status Validation:**
        - Only orders with status 'New', 'Confirmed', 'Processing', or 'Dispatched' can be delivered
        - Already delivered orders return 404 error

        **Event-Driven Architecture:**
        - Uses OrderDeliveredEvent and OrderDeliveredListener
        - Asynchronous wallet processing via queue system
        - Comprehensive logging for audit trail
      operationId: markOrderAsDelivered
      tags:
        - Order Management
      parameters:
        - name: orderId
          in: path
          required: true
          schema:
            type: integer
          description: The ID of the order to mark as delivered
          example: 127810
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MarkOrderDeliveredRequest'
      responses:
        '200':
          description: Order marked as delivered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarkOrderDeliveredResponse'
              example:
                success: true
                message: "Order marked as delivered successfully"
                data:
                  order_id: 127810
                  status: "Complete"
                  delivery_status: "Delivered"
                  delivery_person_id: 123
        '404':
          description: Order not found or already delivered
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                message: "Order not found or already delivered"
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
        '500':
          description: Failed to mark order as delivered
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                message: "Failed to mark order as delivered: Database error"

components:
  schemas:
    CreateOrderRequest:
      type: object
      required:
        - customer_id
        - user_id
        - company_id
        - unit_id
        - fk_kitchen_code
        - customer_address
        - location_code
        - city
        - customer_name
        - customer_email
        - customer_phone
        - meals
        - start_date
        - end_date
        - selected_days
        - subscription_days
        - delivery_time
        - delivery_end_time
        - food_preference
      properties:
        customer_id:
          type: integer
          description: Customer ID (details fetched from customer table)
          example: 3187
        user_id:
          type: integer
          description: ID of the user placing the order
          example: 1
        company_id:
          type: integer
          description: Company ID for the order
          example: 8163
        unit_id:
          type: integer
          description: Unit/Department ID
          example: 8163
        fk_kitchen_code:
          type: integer
          description: Kitchen code for the order
          example: 1
        customer_name:
          type: string
          description: Name of the customer
          example: "Navin dinesh"
        customer_email:
          type: string
          format: email
          description: Email of the customer
          example: "<EMAIL>"
        customer_phone:
          type: string
          description: Phone number of the customer
          example: "7738039366"
        customer_address:
          type: string
          maxLength: 1000
          description: Delivery address for this order
          example: "123 Main Street, Apartment 4B"
        location_code:
          type: integer
          description: |
            Location code that must exist in delivery_locations table.
            The system validates this against the database and returns an error if invalid.
            Location name is automatically fetched from the database.
          example: 101
        city:
          type: integer
          description: |
            City ID that must exist in city table for the specified company and unit.
            The system validates this against the database and returns an error if invalid.
            City name is automatically fetched from the database.
          example: 1
          example: "9"
        is_express:
          type: boolean
          description: Whether this is an express order
          default: false
        meals:
          type: array
          items:
            $ref: '#/components/schemas/MealCartItem'
          description: Array of meals in cart
        start_date:
          type: string
          format: date
          description: "Start date for subscription in YYYY-MM-DD format"
          example: "2025-08-22"
        end_date:
          type: string
          format: date
          description: "End date for subscription in YYYY-MM-DD format"
          example: "2025-08-23"
        selected_days:
          type: array
          items:
            type: integer
            minimum: 0
            maximum: 6
          description: "Array of selected day numbers (0=Sunday, 1=Monday, 2=Tuesday, 3=Wednesday, 4=Thursday, 5=Friday, 6=Saturday)"
          example: [1, 2, 3, 4, 5]
        delivery_time:
          type: string
          format: time
          description: "Start time for delivery in HH:MM:SS format"
          example: "08:00:00"
        delivery_end_time:
          type: string
          format: time
          description: "End time for delivery in HH:MM:SS format"
          example: "09:00:00"
        food_preference:
          type: string
          maxLength: 50
          description: "Food preference (veg/non-veg/eggitarian)"
          example: "veg"
        promo_code:
          type: object
          description: |
            Optional promo code object for applying discounts.
            If applied is true, the code will be validated against the database.
            Invalid codes will result in 422 error.
          properties:
            applied:
              type: boolean
              description: Whether promo code is applied
              example: true
            code:
              type: string
              nullable: true
              maxLength: 50
              description: Promo code string (required if applied is true, can be null if applied is false)
              example: "SPECIAL"
        subscription_days:
          type: integer
          minimum: 1
          maximum: 30
          description: "Number of days for subscription"
          example: 1

    MealCartItem:
      type: object
      required:
        - product_code
        - product_name
        - quantity
        - amount
      properties:
        product_code:
          type: integer
          description: Product code (details fetched from products table)
          example: 339
        product_name:
          type: string
          description: Name of the product
          example: "Indian Breakfast"
        quantity:
          type: integer
          minimum: 1
          description: Quantity of this meal
          example: 1
        amount:
          type: number
          format: float
          description: Price for this quantity of the product
          example: 75.00

    CreatePreOrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Pre-order created successfully"
        data:
          type: object
          properties:
            temp_pre_order_id:
              type: integer
              example: 46154
              description: "Temporary pre-order ID for tracking"
            order_no:
              type: string
              example: "ORD202507221240574531"
              description: "Unique order number"
            customer_id:
              type: integer
              example: 3800
            amount:
              type: number
              example: 200
            status:
              type: string
              example: "pending"
              description: "Pre-order status (pending until payment)"
            days_preference:
              type: string
              example: "1,2,3,4,5"
            subscription_days:
              type: integer
              example: 15
              description: "Total subscription duration in days"
            discount_details:
              type: object
              nullable: true
              description: "Detailed breakdown of applied discounts (double promo support)"
              properties:
                manual_discount:
                  type: object
                  description: "Manual promo code discount details"
                  properties:
                    applied:
                      type: boolean
                      example: true
                    amount:
                      type: number
                      example: 25.00
                    code:
                      type: string
                      nullable: true
                      example: "SPECIAL"
                    details:
                      type: object
                      nullable: true
                plan_discount:
                  type: object
                  description: "Plan-based promo discount details"
                  properties:
                    applied:
                      type: boolean
                      example: true
                    amount:
                      type: number
                      example: 15.00
                    code:
                      type: string
                      nullable: true
                      example: "PLAN5DAY"
                    details:
                      type: object
                      nullable: true
                total_discount:
                  type: number
                  example: 40.00
                  description: "Total combined discount amount"
            manual_promo_code:
              type: string
              nullable: true
              example: "SPECIAL"
              description: "Applied manual promo code (if any)"
            system_promo_code:
              type: string
              nullable: true
              example: "PLAN5DAY"
              description: "Applied system/plan-based promo code (if any)"
            total_discount_amount:
              type: number
              example: 40.00
              description: "Total discount amount applied (sum of manual + plan discounts)"
            payment_transaction_id:
              type: integer
              example: 30609
              description: "Local payment transaction ID"
            payment_service_transaction_id:
              type: string
              example: "TXN30610"
              description: "Payment Service v12 transaction ID"
            payment_urls:
              type: object
              properties:
                process_payment:
                  type: string
                  example: "http://*************:8002/api/v2/payments/TXN30610/process"
                  description: "URL to process payment in Payment Service"
                payment_status:
                  type: string
                  example: "http://*************:8002/api/v2/payments/TXN30610"
                  description: "URL to check payment status in Payment Service"
                order_status:
                  type: string
                  example: "http://*************:8003/api/v2/order-management/pre-order-status/ORD202507221240574531"
                  description: "URL to check pre-order status"

    CreateOrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Order created successfully"
        data:
          type: object
          properties:
            order_id:
              type: integer
              example: 127616
            order_no:
              type: string
              example: "ORD202507220800028674"
            customer_id:
              type: integer
              example: 3800
            amount:
              type: number
              example: 125
            status:
              type: string
              example: "New"
            days_preference:
              type: string
              example: "1,2,3,4,5"
            local_transaction_id:
              type: integer
              example: 30604
              description: "Local transaction ID in QuickServe service"
            payment_service_transaction_id:
              type: integer
              example: 15234
              description: "Transaction ID from Payment Service v12"
            payment_urls:
              type: object
              properties:
                process_payment:
                  type: string
                  example: "http://localhost:8002/api/v2/payments/15234/process"
                  description: "URL to process payment in Payment Service"
                payment_status:
                  type: string
                  example: "http://localhost:8002/api/v2/payments/15234"
                  description: "URL to check payment status in Payment Service"
                order_status:
                  type: string
                  example: "http://localhost:8003/api/v2/order-management/details/ORD202507220800028674"
                  description: "URL to check order status in QuickServe"

    OrderDetailsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            order:
              $ref: '#/components/schemas/OrderDetails'
            meal_items:
              type: array
              items:
                $ref: '#/components/schemas/MealItemDetails'
            payment_transaction:
              $ref: '#/components/schemas/PaymentTransactionDetails'

    OrderDetails:
      type: object
      properties:
        order_id:
          type: integer
        order_no:
          type: string
        customer_id:
          type: integer
        customer_name:
          type: string
        customer_email:
          type: string
        customer_phone:
          type: string
        customer_address:
          type: string
          description: "Full customer address from orders table"
          example: "satish, Nursery, I, 8th Floor, no"
        student_name:
          type: string
          description: "Student name extracted from customer address (0th index after splitting)"
          example: "satish"
        product_name:
          type: string
        product_type:
          type: string
          enum: [Breakfast, Lunch, Dinner, Meal]
          description: "Enhanced product type based on product code and name analysis"
          example: "Lunch"
        original_product_type:
          type: string
          description: "Original product type from database (usually 'Meal')"
          example: "Meal"
        product_code:
          type: integer
          description: "Product code from orders table"
          example: 336
        quantity:
          type: integer
          description: "Quantity of the product ordered"
          example: 1
        item_amount:
          type: string
          description: "Amount for this specific item"
          example: "75.00"
        quantity:
          type: integer
        amount:
          type: string
        order_status:
          type: string
          enum: [New, Confirmed, Scheduled, Payment Failed, Cancelled, Delivered]
        payment_mode:
          type: string
          nullable: true
        amount_paid:
          type: string
        days_preference:
          type: string
        delivery_status:
          type: string
        order_date:
          type: string
          format: date-time
        delivery_time:
          type: string
          format: time
        delivery_end_time:
          type: string
          format: time
        recurring_status:
          type: string

    MealItemDetails:
      type: object
      properties:
        product_code:
          type: integer
        product_name:
          type: string
        quantity:
          type: integer
        amount:
          type: string

    PaymentTransactionDetails:
      type: object
      properties:
        transaction_id:
          type: integer
        gateway_transaction_id:
          type: string
          nullable: true
        status:
          type: string
          enum: [pending, completed, failed]
        gateway:
          type: string
        payment_amount:
          type: string
        created_date:
          type: string
          format: date-time

    CustomerOrdersResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/OrderSummary'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    OrderSummary:
      type: object
      properties:
        pk_order_no:
          type: integer
        order_no:
          type: string
        customer_code:
          type: integer
        customer_name:
          type: string
        product_name:
          type: string
        image_path:
          type: string
          nullable: true
          description: Full S3 URL of the product image
          example: "https://s3.ap-south-1.amazonaws.com/onefooddialer-assets/8163/cms/breakfast-meal.jpg"
        amount:
          type: string
        order_status:
          type: string
        order_date:
          type: string
          format: date-time
        payment_mode:
          type: string
          nullable: true
        amount_paid:
          type: string
        days_preference:
          type: string
        recurring_status:
          type: string
        is_cancellable:
          type: boolean
          description: "Whether this order can be cancelled based on current time and policies"
          example: true
        cancellation_policy:
          type: object
          description: "Cancellation policy details for this order"
          properties:
            refund_percentage:
              type: integer
              description: "Percentage of refund if cancelled now"
              example: 50
            policy_type:
              type: string
              enum: [full_refund_before_cutoff, partial_refund_window, no_cancellation_after_8am, error_no_cancellation]
              description: "Type of cancellation policy currently applicable"
              example: "partial_refund_window"
            cutoff_time:
              type: string
              format: time
              description: "Cutoff time for this meal type"
              example: "00:01:00"
            cutoff_day:
              type: integer
              description: "Days before delivery when cutoff applies (0=same day, 1=day before)"
              example: 0

    PaginationInfo:
      type: object
      properties:
        current_page:
          type: integer
        per_page:
          type: integer
        total:
          type: integer
        last_page:
          type: integer

    PaymentStatusResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            order_no:
              type: string
            order_status:
              type: string
            amount:
              type: string
            amount_paid:
              type: string
            payment_mode:
              type: string
              nullable: true
            payment_transaction:
              $ref: '#/components/schemas/PaymentTransactionDetails'

    PaymentWebhookRequest:
      type: object
      properties:
        order_no:
          type: string
        status:
          type: string
          enum: [completed, success, failed, failure, pending]
        transaction_id:
          type: string
        amount:
          type: number
        gateway:
          type: string
        gateway_transaction_id:
          type: string

    PaymentCallbackRequest:
      type: object
      properties:
        order_no:
          type: string
        status:
          type: string
        transaction_id:
          type: string
        amount:
          type: number
        gateway:
          type: string

    WebhookResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            success:
              type: boolean
            message:
              type: string
            order_id:
              type: integer
            order_no:
              type: string
            status:
              type: string

    PaymentSimulationResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            success:
              type: boolean
            message:
              type: string
            order_id:
              type: integer
            order_no:
              type: string
            status:
              type: string
            amount_paid:
              type: number
            reason:
              type: string
              description: "Present only for failure scenarios"

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Error description"

    PaymentCallbackResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Payment success processed"
        data:
          type: object
          properties:
            order_no:
              type: string
              example: "ORD202507220800028674"
            order_status:
              type: string
              example: "Confirmed"
            payment_status:
              type: string
              example: "completed"
            failure_reason:
              type: string
              description: "Present only for failure callbacks"
              example: "Insufficient funds"

    PreOrderStatusResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            pre_order:
              type: object
              properties:
                temp_pre_order_id:
                  type: integer
                  example: 46154
                order_no:
                  type: string
                  example: "ORD202507221240574531"
                customer_id:
                  type: integer
                  example: 3800
                customer_name:
                  type: string
                  example: "Customer User"
                product_name:
                  type: string
                  example: "International Breakfast Subscription"
                amount:
                  type: string
                  example: "200.00"
                order_status:
                  type: string
                  example: "New"
                days_preference:
                  type: string
                  example: "1,2,3,4,5"
                order_days:
                  type: array
                  items:
                    type: string
                    format: date
                  example: ["2025-07-22", "2025-07-23", "2025-07-24", "2025-07-25", "2025-07-28"]
                  description: "Calculated delivery dates based on days_preference and subscription_days"
                created_date:
                  type: string
                  format: date-time
                  example: "2025-07-22 18:10:57"
            payment_status:
              type: object
              properties:
                temp_payment_status:
                  type: string
                  enum: [pending, success, failed]
                  example: "success"
                  description: "Status from temp_order_payment table"
                transaction_status:
                  type: string
                  enum: [initiated, completed, failed]
                  example: "completed"
                  description: "Status from payment_transaction table"
                gateway:
                  type: string
                  example: "razorpay"
                gateway_transaction_id:
                  type: string
                  example: "TXN30610"
            orders_created:
              type: object
              properties:
                count:
                  type: integer
                  example: 12
                  description: "Number of actual orders created (one per delivery day)"
                order_details_count:
                  type: integer
                  example: 36
                  description: "Number of order_details records created (orders × meal_items)"
                status:
                  type: string
                  enum: [pending, completed]
                  example: "completed"
                  description: "Whether actual orders have been created"

    CancelOrderRequest:
      type: object
      required:
        - reason
        - cancel_dates
      properties:
        reason:
          type: string
          maxLength: 500
          description: "Reason for order cancellation"
          example: "Customer requested cancellation due to change in plans"
        cancel_dates:
          type: array
          minItems: 1
          items:
            type: string
            format: date
          description: "Required: Specific dates to cancel. Since one order_no can span multiple days (5 days, 20 days, etc.), you must specify which delivery dates to cancel"
          example: ["2025-08-06", "2025-08-13", "2025-08-20"]
        meal_type:
          type: string
          enum: [breakfast, lunch, dinner]
          description: "Optional: Filter cancellation by specific meal type"
          example: "lunch"
        request_timestamp:
          type: string
          format: date-time
          description: "Optional: Client-side timestamp when request was initiated (for edge case validation)"
          example: "2025-01-28 07:59:59"

    CancelOrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Orders cancelled successfully with time-based refund policy"
        data:
          type: object
          properties:
            cancelled_orders:
              type: integer
              description: "Number of orders cancelled"
              example: 3
            cancelled_order_ids:
              type: array
              items:
                type: integer
              description: "List of cancelled order IDs"
              example: [127733, 127734, 127735]
            total_refund_amount:
              type: number
              format: decimal
              description: "Total refund amount credited to wallet (after applying time-based policies)"
              example: 125.50
            wallet_credited:
              type: boolean
              description: "Whether refund was successfully credited to wallet"
              example: true
            wallet_unlocked:
              type: number
              format: decimal
              description: "Total wallet amount unlocked from locked state"
              example: 200.00
            customer_code:
              type: integer
              description: "Customer code for the cancelled orders"
              example: 1
            order_no:
              type: string
              description: "Order number that was cancelled"
              example: "XAJE250724"
            refund_breakdown:
              type: array
              description: "Detailed breakdown of refund calculation for each cancelled order"
              items:
                type: object
                properties:
                  order_id:
                    type: integer
                    example: 127733
                  order_date:
                    type: string
                    format: date
                    example: "2025-08-06"
                  meal_type:
                    type: string
                    example: "lunch"
                  base_amount:
                    type: number
                    format: decimal
                    example: 125.00
                  refund_percentage:
                    type: integer
                    example: 50
                  refund_amount:
                    type: number
                    format: decimal
                    example: 62.50
                  wallet_unlocked:
                    type: number
                    format: decimal
                    example: 125.00
                  policy_type:
                    type: string
                    enum: [full_refund_before_cutoff, partial_refund_window, no_cancellation_after_8am, error_no_cancellation]
                    example: "partial_refund_window"
                  cutoff_time:
                    type: string
                    format: time
                    example: "00:01:00"

    ValidationErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Validation failed"
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          example:
            reason: ["The reason field is required."]
            cancel_dates.0: ["The cancel_dates.0 must be a date after or equal to today."]

    CancellationSettingsResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            cutoff_times:
              type: object
              properties:
                breakfast:
                  type: object
                  properties:
                    cutoff_time:
                      type: string
                      format: time
                      example: "00:01:00"
                    cutoff_day:
                      type: integer
                      example: 0
                lunch:
                  type: object
                  properties:
                    cutoff_time:
                      type: string
                      format: time
                      example: "00:01:00"
                    cutoff_day:
                      type: integer
                      example: 0
            refund_percentages:
              type: object
              properties:
                breakfast_partial_refund:
                  type: integer
                  example: 0
                lunch_partial_refund:
                  type: integer
                  example: 50
            time_windows:
              type: object
              properties:
                partial_refund_start:
                  type: string
                  format: time
                  example: "00:01:00"
                partial_refund_end:
                  type: string
                  format: time
                  example: "08:00:00"
                no_refund_start:
                  type: string
                  format: time
                  example: "08:00:01"
            feature_flags:
              type: object
              properties:
                time_based_cancellation_enabled:
                  type: boolean
                  example: true
                wallet_locking_enabled:
                  type: boolean
                  example: true
                automatic_refund_enabled:
                  type: boolean
                  example: true

    SwapOrderRequest:
      type: object
      required:
        - order_date
        - new_product_code
      properties:
        order_date:
          type: string
          format: date
          description: "Date of the order to swap (YYYY-MM-DD)"
          example: "2025-09-03"
        new_product_code:
          type: integer
          description: "Product code of the new product to swap to"
          example: 342
        reason:
          type: string
          maxLength: 255
          description: "Optional reason for the swap"
          example: "Customer wants to change from Poha to Upma"
        meal_type:
          type: string
          enum: [breakfast, lunch, dinner]
          description: "Optional filter by meal type"
          example: "breakfast"

    SwapOrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Order swapped successfully"
        data:
          type: object
          properties:
            order_id:
              type: integer
              description: "Internal order ID"
              example: 127810
            order_no:
              type: string
              description: "Order number that was swapped"
              example: "QA93250725"
            order_date:
              type: string
              format: date
              description: "Date of the swapped order"
              example: "2025-09-03"
            swap_details:
              type: object
              description: "Detailed information about the swap"
              properties:
                old_product:
                  type: object
                  description: "Details of the original product"
                  properties:
                    code:
                      type: integer
                      example: 341
                    name:
                      type: string
                      example: "Poha"
                    price:
                      type: number
                      format: decimal
                      example: 150.00
                new_product:
                  type: object
                  description: "Details of the new product"
                  properties:
                    code:
                      type: integer
                      example: 342
                    name:
                      type: string
                      example: "Upma"
                    price:
                      type: number
                      format: decimal
                      example: 200.00
                price_difference:
                  type: number
                  format: decimal
                  description: "Price difference between old and new product"
                  example: 50.00
                swap_charges:
                  type: number
                  format: decimal
                  description: "Additional charges for the swap"
                  example: 25.00
                total_amount_change:
                  type: number
                  format: decimal
                  description: "Total amount change (price difference + swap charges)"
                  example: 75.00
                new_order_amount:
                  type: number
                  format: decimal
                  description: "New total order amount after swap"
                  example: 225.00
            reason:
              type: string
              description: "Reason provided for the swap"
              example: "Customer wants to change from Poha to Upma"

    MarkOrderDeliveredRequest:
      type: object
      required:
        - delivery_person_id
      properties:
        delivery_person_id:
          type: integer
          description: ID of the delivery person who delivered the order
          example: 123
        delivery_notes:
          type: string
          maxLength: 500
          description: Optional notes about the delivery
          example: "Order delivered successfully to customer at door"

    MarkOrderDeliveredResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Order marked as delivered successfully"
        data:
          type: object
          properties:
            order_id:
              type: integer
              description: The ID of the delivered order
              example: 127810
            status:
              type: string
              description: Updated order status
              example: "Complete"
            delivery_status:
              type: string
              description: Updated delivery status
              example: "Delivered"
            delivery_person_id:
              type: integer
              description: ID of the delivery person
              example: 123

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: "JWT token for authenticated endpoints"

tags:
  - name: Order Management
    description: "Core order management operations"
  - name: Payment Management
    description: "Payment status and tracking"
  - name: Payment Processing
    description: "Payment gateway integration"
  - name: Payment Callbacks
    description: "Payment service callback handlers"
  - name: Testing
    description: "Testing and simulation endpoints"
