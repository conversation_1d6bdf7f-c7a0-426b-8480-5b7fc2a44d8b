<?php

/**
 * Test script to verify double promo functionality
 * Tests that both manual promo codes and plan-based discounts can be applied together
 */

require_once __DIR__ . '/bootstrap/app.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

echo "=== Double Promo Test ===\n\n";

// Test configuration
$baseUrl = 'http://*************:8003/api/v2/order-management/orders';
$testCustomerId = 3800;
$testCompanyId = 8163;
$testUnitId = 8163;
$testKitchenCode = 1;

// Test 1: Create order with manual promo code that should also get plan-based discount
echo "Test 1: Creating order with manual promo code 'Special' for 5-day subscription...\n";

$orderPayload = [
    'customer_id' => $testCustomerId,
    'customer_address' => '123 Test Street, Test Area',
    'location_code' => 1,
    'city' => 9,
    'company_id' => $testCompanyId,
    'unit_id' => $testUnitId,
    'fk_kitchen_code' => $testKitchenCode,
    'meals' => [
        ['product_code' => 342, 'quantity' => 1]
    ],
    'start_date' => date('Y-m-d', strtotime('+1 day')),
    'selected_days' => [1, 2, 3, 4, 5], // Monday to Friday
    'subscription_days' => 5, // This should trigger plan-based promo
    'delivery_time' => '08:00:00',
    'delivery_end_time' => '09:00:00',
    'food_preference' => 'veg',
    'payment_method' => 'online',
    'promo_code' => [
        'applied' => true,
        'code' => 'Special' // Manual promo code
    ]
];

// Make API call
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($orderPayload));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Status: $httpCode\n";

if ($response === false) {
    echo "❌ CURL Error occurred\n";
    exit(1);
}

$responseData = json_decode($response, true);

if ($httpCode === 201 && isset($responseData['success']) && $responseData['success']) {
    echo "✅ Order created successfully!\n";
    
    // Check discount details
    $discountDetails = $responseData['data']['discount_details'] ?? null;
    $totalDiscountAmount = $responseData['data']['total_discount_amount'] ?? 0;
    $manualPromoCode = $responseData['data']['manual_promo_code'] ?? null;
    $systemPromoCode = $responseData['data']['system_promo_code'] ?? null;
    
    echo "\n--- Discount Analysis ---\n";
    echo "Total Discount Amount: ₹$totalDiscountAmount\n";
    echo "Manual Promo Code: " . ($manualPromoCode ?: 'None') . "\n";
    echo "System Promo Code: " . ($systemPromoCode ?: 'None') . "\n";
    
    if ($discountDetails) {
        echo "\nDetailed Discount Breakdown:\n";
        
        // Manual discount
        $manualDiscount = $discountDetails['manual_discount'] ?? [];
        if ($manualDiscount['applied'] ?? false) {
            echo "  Manual Promo ('{$manualDiscount['code']}'): ₹{$manualDiscount['amount']}\n";
        } else {
            echo "  Manual Promo: Not Applied\n";
        }
        
        // Plan discount
        $planDiscount = $discountDetails['plan_discount'] ?? [];
        if ($planDiscount['applied'] ?? false) {
            echo "  Plan Promo ('{$planDiscount['code']}'): ₹{$planDiscount['amount']}\n";
        } else {
            echo "  Plan Promo: Not Applied\n";
        }
        
        echo "  Total Combined Discount: ₹{$discountDetails['total_discount']}\n";
        
        // Check if double promo was applied
        $doublePromoApplied = ($manualDiscount['applied'] ?? false) && ($planDiscount['applied'] ?? false);
        if ($doublePromoApplied) {
            echo "\n🎉 SUCCESS: Double promo applied successfully!\n";
            echo "   Both manual promo code and plan-based discount were applied.\n";
        } else {
            echo "\n⚠️  WARNING: Double promo not fully applied.\n";
            if (!($manualDiscount['applied'] ?? false)) {
                echo "   Manual promo code was not applied.\n";
            }
            if (!($planDiscount['applied'] ?? false)) {
                echo "   Plan-based discount was not applied.\n";
            }
        }
    } else {
        echo "⚠️  No discount details found in response\n";
    }
    
    // Store order details for verification
    $orderNo = $responseData['data']['order_no'] ?? null;
    $tempPreOrderIds = $responseData['data']['temp_pre_order_ids'] ?? [];
    
    echo "\nOrder Details:\n";
    echo "  Order No: $orderNo\n";
    echo "  Temp Pre-Order IDs: " . implode(', ', array_column($tempPreOrderIds, 'id')) . "\n";
    echo "  Total Amount: ₹{$responseData['data']['total_amount']}\n";
    
} else {
    echo "❌ Order creation failed!\n";
    echo "Response: $response\n";
    
    if (isset($responseData['message'])) {
        echo "Error: {$responseData['message']}\n";
    }
}

echo "\n" . str_repeat("=", 60) . "\n\n";

// Test 2: Verify database records
if (isset($orderNo) && isset($tempPreOrderIds) && !empty($tempPreOrderIds)) {
    echo "Test 2: Verifying database records...\n";
    
    foreach ($tempPreOrderIds as $tempPreOrderData) {
        $tempPreOrderId = $tempPreOrderData['id'];
        
        // Check temp_pre_orders record
        $tempPreOrder = DB::table('temp_pre_orders')
            ->where('pk_order_no', $tempPreOrderId)
            ->first(['promo_code', 'system_promo_code', 'applied_discount']);
        
        if ($tempPreOrder) {
            echo "\nTemp Pre-Order ID $tempPreOrderId:\n";
            echo "  Manual Promo Code: " . ($tempPreOrder->promo_code ?: 'None') . "\n";
            echo "  System Promo Code: " . ($tempPreOrder->system_promo_code ?: 'None') . "\n";
            echo "  Applied Discount: ₹$tempPreOrder->applied_discount\n";
        } else {
            echo "❌ Temp pre-order $tempPreOrderId not found in database\n";
        }
    }
    
    // Check payment_transaction record
    $paymentTransaction = DB::table('payment_transaction')
        ->where('pre_order_id', $orderNo)
        ->first(['promo_code', 'discount', 'metadata']);
    
    if ($paymentTransaction) {
        echo "\nPayment Transaction:\n";
        echo "  Promo Code: " . ($paymentTransaction->promo_code ?: 'None') . "\n";
        echo "  Discount: ₹$paymentTransaction->discount\n";
        
        if ($paymentTransaction->metadata) {
            $metadata = json_decode($paymentTransaction->metadata, true);
            if (isset($metadata['discount_breakdown'])) {
                echo "  Metadata Discount Breakdown:\n";
                $breakdown = $metadata['discount_breakdown'];
                echo "    Manual: ₹{$breakdown['manual_promo']['discount_amount']} ({$breakdown['manual_promo']['code']})\n";
                echo "    Plan: ₹{$breakdown['plan_promo']['discount_amount']} ({$breakdown['plan_promo']['code']})\n";
                echo "    Total: ₹{$breakdown['total_discount']}\n";
            }
        }
    } else {
        echo "❌ Payment transaction not found for order $orderNo\n";
    }
}

echo "\n=== Test Complete ===\n";
