<?php

/**
 * Test script to verify express delivery charge API response
 */

$baseUrl = 'http://127.0.0.1:8000/api/v2/order-management/create';

$orderPayload = [
    'is_express' => true,
    'customer_id' => 1,
    'user_id' => 1,
    'company_id' => 8163,
    'unit_id' => 8163,
    'fk_kitchen_code' => 1,
    'customer_name' => '<PERSON><PERSON>',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '918793644824',
    'customer_address' => 'child3, 3, H, 5th Floor',
    'location_code' => 17,
    'location_name' => 'Default Location',
    'city' => 9,
    'city_name' => 'Mumbai',
    'start_date' => '2025-09-08',
    'selected_days' => [1, 2],
    'subscription_days' => 2,
    'meals_by_date' => [
        [
            'date' => '2025-09-08',
            'meals' => [
                [
                    'product_code' => 336,
                    'quantity' => 1,
                    'charge' => 50
                ]
            ]
        ],
        [
            'date' => '2025-09-09',
            'meals' => [
                [
                    'product_code' => 336,
                    'quantity' => 1,
                    'charge' => 0
                ]
            ]
        ]
    ],
    'promo_code' => [
        'applied' => false,
        'code' => null
    ]
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($orderPayload));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json',
    'Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJVa29NTHlmWkRXQ1k2TkdQQy1yMEtQb09kdURDd21KQkdjaVdwenFzMC04In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CCDLS93gY3d00SyjQQQeFt_NB9m5aQJbd7g9dMaZ7x5m-gYoljpfRhxN7gdYuvAdBJmKKdaLUo2mNZJjbszPfyvvyYWNXRgKt4OeO68HgSXq8gGmS7CkLid0LAn9Gu8HnLJAsPG81UlnCO5uQCcnUjIazyB5LWvoU-j-lRj_c0TTqRZI0wySK9vbB8hqOF9DuVLajjEbEJX9Tu_ow3-p0qhzTPgAP7a5ivqZgecRBvJFEE4fER7_4dFeKYbRoWnGhniUGE7ms9FMfNP_WJsv-KbUPgdwGZptfmezoNeKQl_-W7iu6sgfVNkJEwdp9E9nF69NtQkNxqrFrP0p1sJIuQ'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

echo "=== Express Delivery Charge Test ===\n\n";
echo "Testing cURL request...\n";

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Status: $httpCode\n";

if ($response === false) {
    echo "❌ CURL Error occurred\n";
    exit(1);
}

$responseData = json_decode($response, true);

if ($httpCode === 201 && isset($responseData['success']) && $responseData['success']) {
    echo "✅ Order created successfully!\n\n";
    
    $data = $responseData['data'];
    $totalAmount = $data['total_amount'] ?? 0;
    $orderNo = $data['order_no'] ?? null;
    
    echo "Order Details:\n";
    echo "  Order No: $orderNo\n";
    echo "  Total Amount: ₹$totalAmount\n";
    
    // Check temp pre-order details
    $tempPreOrderIds = $data['temp_pre_order_ids'] ?? [];
    
    if (!empty($tempPreOrderIds)) {
        echo "\nTemp Pre-Order Analysis:\n";
        
        $totalExpressCharges = 0;
        $totalMealAmount = 0;
        
        foreach ($tempPreOrderIds as $tempPreOrderData) {
            $tempPreOrderId = $tempPreOrderData['id'];
            $deliveryDate = $tempPreOrderData['delivery_date'] ?? 'Unknown';
            
            echo "  Date $deliveryDate (ID: $tempPreOrderId):\n";
            echo "    Amount: ₹{$tempPreOrderData['amount']}\n";
            echo "    Express Charge: ₹{$tempPreOrderData['express_delivery_charge']}\n";
            echo "    Tax: ₹{$tempPreOrderData['tax_amount']}\n";
            echo "    Total: ₹{$tempPreOrderData['total_amount']}\n";
            
            $totalExpressCharges += $tempPreOrderData['express_delivery_charge'];
            $totalMealAmount += $tempPreOrderData['amount'];
        }
        
        echo "\nSummary:\n";
        echo "  Total Meal Amount: ₹$totalMealAmount\n";
        echo "  Total Express Charges: ₹$totalExpressCharges\n";
        echo "  Final Total: ₹$totalAmount\n";
        
        if ($totalExpressCharges > 0) {
            echo "\n🎉 SUCCESS: Express delivery charges applied correctly!\n";
            echo "   Express charge of ₹$totalExpressCharges was added to the order.\n";
            
            // Verify the expected charges
            $expectedExpressCharge = 50; // From 2025-09-08 with charge: 50
            if ($totalExpressCharges == $expectedExpressCharge) {
                echo "   ✅ Express charge matches expected amount (₹$expectedExpressCharge).\n";
            } else {
                echo "   ⚠️  Express charge (₹$totalExpressCharges) doesn't match expected (₹$expectedExpressCharge).\n";
            }
        } else {
            echo "\n❌ ISSUE: No express delivery charges were applied.\n";
        }
    }
    
} else {
    echo "❌ Order creation failed!\n";
    echo "Response: $response\n";
    
    if (isset($responseData['message'])) {
        echo "Error: {$responseData['message']}\n";
    }
}

echo "\n" . str_repeat("=", 60) . "\n";
